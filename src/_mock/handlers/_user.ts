import { faker } from '@faker-js/faker';
import { http, HttpResponse } from 'msw';

import type { PagaginateResult, UserInfo } from '@/types/entity';
import { ResultStats, UserApi } from '@/types/enum';

import { USERS, MOCK_USER_LIST } from '../assets';

const signIn = http.post(`${UserApi.SignIn}`, async ({ request }) => {
    console.log('MSW intercepted signin request:', request.url);
    const { username, password } = (await request.json()) as Record<string, string>;
    console.log('Login attempt:', { username, password });

    const user = USERS.find((item) => item.username === username);
    if (!user || user.password !== password) {
        return HttpResponse.json({
            status: 10001,
            message: 'Incorrect username or password.',
        });
    }

    // delete password
    const { password: _, ...userWithoutPassword } = user;
    const response = {
        status: ResultStats.SUCCESS,
        message: '',
        data: {
            userInfo: { ...userWithoutPassword },
            accessToken: faker.string.uuid(),
            refreshToken: faker.string.uuid(),
        },
    };

    console.log('MSW returning response:', response);
    return HttpResponse.json(response);
});

const userList = http.get(`${UserApi.UserList}`, async ({ request }) => {
    console.log('MSW intercepted user list request:', request.url);

    const url = new URL(request.url);
    const current = parseInt(url.searchParams.get('current') || '1', 10);
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

    console.log('分页参数:', { current, pageSize });

    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 300));

    // 计算分页数据
    const startIndex = (current - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const items = MOCK_USER_LIST.slice(startIndex, endIndex);
    const totalItems = MOCK_USER_LIST.length;
    const totalPages = Math.ceil(totalItems / pageSize);

    const response: PagaginateResult<UserInfo> = {
        items,
        meta: {
            currentPage: current,
            itemCount: items.length,
            perPage: pageSize,
            totalItems,
            totalPages,
        },
    };

    console.log('MSW returning user list response:', response);

    return HttpResponse.json({
        status: ResultStats.SUCCESS,
        message: '获取用户列表成功',
        data: response,
    });
});

export { signIn, userList };
