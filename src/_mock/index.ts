import { getMenuTree } from './handlers/_menu';
import { signIn, userList } from './handlers/_user';

const handlers = [signIn, userList, getMenuTree];

// Create a function to get worker only in browser environment
const getWorker = async () => {
    if (typeof window !== 'undefined') {
        const { setupWorker } = await import('msw/browser');
        return setupWorker(...handlers);
    }
    return null;
};

export { getWorker };
