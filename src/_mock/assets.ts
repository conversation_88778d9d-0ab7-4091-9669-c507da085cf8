import { faker } from '@faker-js/faker';

import type { User, UserInfo } from '@/types/entity';
import { BasicStatus } from '@/types/enum';

import WorkbenchPage from '@/app/workbench/page';

export const USERS: User[] = [
    {
        id: 'user_admin_id',
        username: 'admin',
        password: 'demo1234',
        avatar: faker.image.avatarGitHub(),
        email: '<EMAIL>',
    },
    {
        id: 'user_test_id',
        username: 'test',
        password: 'demo1234',
        avatar: faker.image.avatarGitHub(),
        email: '<EMAIL>',
    },
    {
        id: 'user_guest_id',
        username: 'guest',
        password: 'demo1234',
        avatar: faker.image.avatarGitHub(),
        email: '<EMAIL>',
    },
];

// 生成更多用户数据用于分页测试
export const MOCK_USER_LIST: UserInfo[] = Array.from({ length: 50 }, (_, index) => ({
    id: faker.string.uuid(),
    username: faker.internet.username(),
    email: faker.internet.email(),
    phone: faker.phone.number(),
    avatar: faker.image.avatarGitHub(),
    status: index % 3 === 0 ? BasicStatus.DISABLED : BasicStatus.ENABLED,
    roles: [
        {
            id: faker.string.uuid(),
            name: faker.helpers.arrayElement(['管理员', '编辑者', '查看者', '普通用户']),
            code: faker.helpers.arrayElement(['admin', 'editor', 'viewer', 'user']),
        },
    ],
}));

export const preDefinedRoutes = [
    {
        path: '/',
        name: '首页',
        exact: true,
        key: 'home',
        icon: '',
        component: null,
    },
    {
        path: '/workspace',
        name: '工作台',
        exact: true,
        key: 'workspace',
        component: WorkbenchPage,
        icon: '',
        // icon: () =>
        //   React.createElement(Icon, { icon: 'arcticons:syska-smart-home' })
    },
    {
        path: '/user',
        name: '用户管理',
        key: 'user',
        type: 'subMenu',
        icon: '',
        iconfont: 'icon-xiaoshouzongjian',
        routes: [
            {
                path: '/user/list',
                name: '用户列表',
                exact: true,
                key: 'user:list:view',
                component: null,
            },
            {
                path: '/user/list/add',
                name: '新增用户',
                exact: true,
                key: 'user:list:add',
                // hideInMenu: true,
                component: null,
            },
            {
                path: '/user/list/edit',
                name: '编辑用户',
                exact: true,
                key: 'user:list:edit',
                hideInMenu: true,
                component: null,
            },
        ],
    },
    {
        path: '/role',
        name: '角色管理',
        key: 'role',
        type: 'subMenu',
        icon: '',
        routes: [
            {
                path: '/role/list',
                name: '角色列表',
                exact: true,
                key: 'role:list:view',
                component: null,
            },
        ],
    },
    {
        path: '/auth',
        name: '权限测试页',
        exact: true,
        key: 'auth:test:view',
        icon: '',
        component: null,
    },
    {
        path: '/test-api',
        name: '测试api',
        exact: true,
        key: '/test-api',
        icon: '',
        component: null,
    },
    {
        path: '/403',
        name: '暂无权限',
        exact: true,
        key: '/403',
        icon: '',
        // hideInMenu: true,
        component: null,
    },
];
